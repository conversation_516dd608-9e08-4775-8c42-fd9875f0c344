<template>
	<view class="content" :style="background ? 'background: url(' + background + ') no-repeat top; background-size: 160%;' : ''">
		<!-- 顶部导航 -->
		<view class="zhuige-idle-header" :style="background ? 'background: url(' + background + ') no-repeat top; background-size: 160%;' : ''">
			<uni-nav-bar
				:fixed="true"
				:status-bar="true"
				background-color="rgba(255,255,255,0)"
			>
				<view class="zhuige-top-bar">
					<view class="zhuige-top-search">
						<uni-icons type="search" size="18" color="#999999"></uni-icons>
						<input
							class="top-input"
							type="text"
							placeholder="关键词..."
							placeholder-class="top-input-placeholder"
							confirm-type="search"
							v-model="search"
							@confirm="confirmSearch"
						/>
					</view>
				</view>
			</uni-nav-bar>

			<!-- 分类导航 -->
			<view class="zhuige-tab-wide-box" v-if="nav_cats && nav_cats.length > 0">
				<zhuige-tab
					:tabs="nav_cats"
					:curTab="cur_cat"
					type="scroll"
					:opt="true"
					@clickTab="clickCat"
					@clickTabOpt="clickTabOpt"
				></zhuige-tab>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="zhuige-swiper-box">
			<zhuige-swiper :items="slides"></zhuige-swiper>
		</view>

		<!-- 热门图标 -->
		<view class="zhuige-wide-box zhuige-hot-box" v-if="icons && icons.length > 0">
			<zhuige-icons :items="icons" type="scroll"></zhuige-icons>
		</view>
		
		<!-- 商品瀑布流 -->
		<block v-if="lists[0].length > 0 || lists[1].length > 0">
			<view class="zhuige-waterfall">
				<view 
					class="zhuige-waterfall-list" 
					:id="'list-' + listIndex"
					v-for="(list, listIndex) in lists" 
					:key="listIndex"
				>
					<view v-for="(item, index) in list" :key="item.id">
						<!-- 广告位 -->
						<block v-if="list_ad && list_ad.length > 0">
							<view 
								class="zhuige-waterfall-list-ad"
								v-if="(index * 2 + listIndex - ad.first) >= 0 && (index * 2 + listIndex - ad.first) % ad.frequency == 0"
								v-for="(ad, adi) in list_ad" 
								:key="adi"
							>
								<zhuige-swiper :items="ad.items"></zhuige-swiper>
							</view>
						</block>
						
						<!-- 商品卡片 -->
						<view 
							class="zhuige-waterfall-block" 
							:style="item.height ? 'opacity: 1;' : ''"
							@click="clickLink('/pages/idle-shop/detail/detail?id=' + item.id)"
						>
							<view class="zhuige-waterfall-img">
								<view class="waterfall-mark" v-if="item.stick">推广</view>
								<image 
									:src="item.thumb" 
									mode="aspectFill"
									:style="item.height ? 'height:' + item.height + 'rpx;' : ''"
									:data-a="listIndex"
									:data-b="index"
									@load="onImageLoad"
								/>
							</view>
							<view class="zhuige-waterfall-text">
								<view class="title">{{ item.title }}</view>
								<view class="excerpt" v-if="item.excerpt">{{ item.excerpt }}</view>
							</view>
							<view class="zhuige-waterfall-tags" v-if="item.tags && item.tags.length > 0">
								<text v-for="(tag, itag) in item.tags" :key="itag">{{ tag.name }}</text>
							</view>
							<view class="zhuige-waterfall-footer">
								<view class="zhuige-waterfall-user">
									<image mode="aspectFill" :src="item.user.avatar"></image>
									<text>{{ item.user.nickname }}</text>
								</view>
								<view class="zhuige-waterfall-price">
									<text>￥</text>
									<text>{{ item.price }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view style="padding-bottom:100px;">
				<uni-load-more :status="loadMore"></uni-load-more>
			</view>
		</block>
		<block v-else>
			<zhuige-nodata v-if="loaded"></zhuige-nodata>
		</block>
		
		<!-- 底部菜单 -->
		<view 
			class="zhuge-y_pages-tab" 
			:class="'res-tab' + bottom_menu.length"
			v-if="bottom_menu && bottom_menu.length > 0"
		>
			<view 
				class="zhuge-y_pages-tab-icon" 
				v-for="(item, index) in bottom_menu" 
				:key="index"
				@click="clickLink(item.link)"
			>
				<image :src="item.image"></image>
				<view>{{ item.title }}</view>
			</view>
		</view>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	components: {
		ZhuigeSwiper: () => import('@/components/zhuige-swiper'),
		ZhuigeIcons: () => import('@/components/zhuige-icons'),
		ZhuigeTab: () => import('@/components/zhuige-tab'),
		ZhuigeNodata: () => import('@/components/zhuige-nodata')
	},
	data() {
		this.list = []
		this.heights = []
		return {
			search: '',
			background: undefined,
			nav_cats: [],
			cur_cat: undefined,
			slides: [],
			icons: [],
			lists: [[], []],
			loadMore: 'more',
			loaded: false,
			list_ad: undefined,
			bottom_menu: []
		}
	},
	onLoad(options) {
		Share.addShareScore(options.source)
		this.loadSetting()
	},
	onShow() {
		// 页面显示时刷新数据
	},
	onPullDownRefresh() {
		this.loadGoods(true)
	},
	onReachBottom() {
		this.loadGoods(false)
	},
	onShareAppMessage() {
		return {
			title: '闲置物品-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/index/index?n=n')
		}
	},
	onShareTimeline() {
		return {
			title: '闲置物品-' + getApp().globalData.appName
		}
	},
	methods: {
		confirmSearch() {
			if (this.search.trim()) {
				Share.openLink('/pages/base/search/search?search=' + encodeURIComponent(this.search) + '&type=idle')
			}
		},
		clickLink(url) {
			Share.openLink(url)
		},
		clickNavLeft() {
			Share.navigateBack()
		},
		clickCat(cat) {
			if (this.cur_cat != cat.id) {
				this.cur_cat = cat.id
				this.loadGoods(true)
			}
		},
		clickTabOpt() {
			Share.openLink('/pages/idle-shop/classify/classify')
		},
		loadSetting() {
			Rest.post(Api.URL('idle', 'setting'), {}).then(res => {
				if (res.data.background) {
					this.background = res.data.background
				}
				this.icons = res.data.icons
				this.slides = res.data.slides
				this.nav_cats = res.data.nav_cats
				if (this.nav_cats.length > 0) {
					this.cur_cat = this.nav_cats[0].id
					this.loadGoods(true)
				}
				this.list_ad = res.data.list_ad
				this.bottom_menu = res.data.bottom_menu
				if (res.data.share_img) {
					this.share_img = res.data.share_img
				}
				uni.stopPullDownRefresh()
			}, err => {
				console.log(err)
			})
		},
		loadGoods(refresh) {
			if (this.loadMore == 'loading') return
			
			this.loadMore = 'loading'
			Rest.post(Api.URL('idle', 'list'), {
				cat_id: this.cur_cat,
				offset: refresh ? 0 : this.list.length
			}).then(res => {
				if (refresh) {
					this.list = res.data.list
					this.lists = [[], []]
					this.heights = []
				} else {
					this.list = this.list.concat(res.data.list)
				}
				
				// 分配到两列
				this.distributeItems(refresh ? 0 : this.list.length - res.data.list.length)
				
				this.loadMore = res.data.more
				this.loaded = true
				
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			}, err => {
				console.log(err)
				this.loadMore = 'more'
				if (refresh) {
					uni.stopPullDownRefresh()
				}
			})
		},
		distributeItems(startIndex) {
			for (let i = startIndex; i < this.list.length; i++) {
				const item = this.list[i]
				const leftHeight = this.getColumnHeight(0)
				const rightHeight = this.getColumnHeight(1)
				
				if (leftHeight <= rightHeight) {
					this.lists[0].push(item)
				} else {
					this.lists[1].push(item)
				}
			}
		},
		getColumnHeight(columnIndex) {
			let height = 0
			this.lists[columnIndex].forEach((item, index) => {
				height += item.height || 300
			})
			return height
		},
		onImageLoad(e) {
			const { a: listIndex, b: index } = e.target.dataset
			const { width, height } = e.detail
			
			// 计算图片高度
			const imageHeight = Math.floor(height * 345 / width)
			const item = this.lists[listIndex][index]
			
			if (item) {
				item.height = imageHeight
				this.$forceUpdate()
			}
		}
	}
}
</script>

<style>
.zhuige-waterfall {
	align-content: flex-start;
	align-items: flex-start;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	justify-content: flex-start;
	padding: 0 10rpx 20rpx;
}

.zhuige-waterfall-list {
	box-sizing: border-box;
	padding: 0 10rpx;
	width: 50%;
}

.zhuige-waterfall-block {
	background-color: #fff;
	border-radius: 12rpx;
	color: #555;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.zhuige-waterfall-img {
	position: relative;
}

.zhuige-waterfall-img image {
	border-radius: 12rpx 12rpx 0 0;
	display: inherit;
	height: 200rpx;
	width: 100%;
}

.waterfall-mark {
	background: #ff6146;
	border-radius: 6rpx 0 6rpx 0;
	color: #fff;
	font-size: 22rpx;
	height: 48rpx;
	left: 0;
	line-height: 48rpx;
	padding: 0 24rpx;
	position: absolute;
	top: 0;
	z-index: 3;
}

.zhuige-waterfall-footer {
	justify-content: space-between;
	padding: 0 16rpx 10rpx;
}

.zhuige-waterfall-footer,
.zhuige-waterfall-user {
	align-items: center;
	display: flex;
}

.zhuige-waterfall-user image {
	border-radius: 50%;
	height: 48rpx;
	margin-right: 12rpx;
	width: 48rpx;
}

.zhuige-waterfall-user text {
	color: #333;
	font-size: 24rpx;
	font-weight: 400;
}

.zhuige-waterfall-text {
	line-height: 1em;
	margin-top: 12rpx;
	padding: 12rpx 16rpx 0;
}

.zhuige-waterfall-text view {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.zhuige-waterfall-text view.title {
	color: #333;
	font-size: 30rpx;
	font-weight: 600;
}

.zhuige-waterfall-text view.excerpt {
	color: #555;
	font-size: 26rpx;
	font-weight: 400;
	margin-top: 26rpx;
}

.zhuige-waterfall-tags {
	display: flex;
	flex-wrap: wrap;
	height: 40rpx;
	margin: 26rpx 0 0;
	overflow: hidden;
	padding: 0 16rpx;
}

.zhuige-waterfall-tags text {
	background: #f5f5f5;
	border-radius: 6rpx;
	color: #333;
	font-size: 20rpx;
	font-weight: 400;
	height: 36rpx;
	line-height: 36rpx;
	margin-right: 8rpx;
	padding: 0 12rpx;
}

.zhuige-waterfall-tags text:nth-child(1) {
	background: #2c70db;
	color: #fff;
}

.zhuige-waterfall-price {
	align-items: center;
	display: flex;
	flex-wrap: nowrap;
	overflow: hidden;
}

.zhuige-waterfall-price text:nth-child(1) {
	color: #333;
	font-size: 24rpx;
	font-weight: 300;
}

.zhuige-waterfall-price text:nth-child(2) {
	color: #333;
	font-size: 36rpx;
	font-weight: 600;
	margin-left: 8rpx;
}

.zhuige-waterfall-list-ad {
	margin-bottom: 20rpx;
}

.zhuige-idle-header {
	left: 0;
	position: fixed;
	width: 100%;
	z-index: 9;
}

.zhuige-tab-wide-box .zhuige-tab,
.zhuige-tab-wide-box .zhuige-tab-opt {
	background: none !important;
}

.zhuige-tab-wide-box .zhuige-tab-nav {
	width: 92%;
}

.zhuige-swiper-box {
	padding: 280rpx 20rpx 20rpx;
}

.zhuige-top-bar {
	left: 10rpx;
}

.zhuige-tab-box:first-of-type {
	margin-left: -24rpx !important;
}

.zhuge-y_pages-tab {
	align-items: center;
	background: #fff;
	border-radius: 32rpx 32rpx 0 0;
	bottom: 0rpx;
	box-shadow: 0 -20rpx 32rpx -18rpx rgba(79, 125, 183, .3);
	display: flex;
	flex-wrap: nowrap;
	height: 168rpx;
	padding: 0 4%;
	position: fixed;
	width: 92%;
	z-index: 999;
}

.zhuge-y_pages-tab-icon {
	margin-bottom: 20rpx;
	text-align: center;
	width: 18%;
}

.res-tab2 .zhuge-y_pages-tab-icon {
	width: 50%;
}

.res-tab4 .zhuge-y_pages-tab-icon {
	width: 25%;
}

.res-tab5 .zhuge-y_pages-tab-icon {
	width: 18%;
}

.zhuge-y_pages-tab-icon:nth-child(1) {
	background: #fff;
	border-radius: 160rpx;
	height: 160rpx;
	margin-top: -48rpx;
	width: 160rpx;
}

.zhuge-y_pages-tab-icon image {
	margin: 4rpx auto;
	height: 64rpx;
	width: 64rpx;
}

.zhuge-y_pages-tab-icon:nth-child(1) image {
	height: 96rpx;
	margin: 24rpx auto 4rpx;
	width: 96rpx;
}

.zhuge-y_pages-tab-icon view {
	color: #666;
	font-size: 24rpx;
	font-weight: 400;
	height: 1em;
	line-height: 1em;
	margin-top: -16rpx;
}

.uni-navbar--border {
	border: none !important;
}

.top-input {
	margin-left: 8rpx;
}

.top-input-placeholder {
	color: #999;
}
</style>
