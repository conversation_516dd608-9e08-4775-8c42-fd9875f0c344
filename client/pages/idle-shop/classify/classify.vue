<template>
	<view class="content">
		<view class="zhuige-classify" v-for="(cat, index) in cats" :key="index">
			<view class="zhuige-idle-classify-header">
				<view>
					<view>{{ cat.name }}</view>
					<text>{{ '总计' + cat.count + '产品' }}</text>
				</view>
				<view @click="clickCat(cat)">查看更多</view>
			</view>
			<view class="zhuige-idle-classify-cover">
				<view 
					v-for="(goods, iGoods) in cat.list" 
					:key="iGoods"
					@click="clickGoods(goods)"
				>
					<image :src="goods.thumb" mode="aspectFill" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	data() {
		return {
			cats: []
		}
	},
	onLoad() {
		this.loadCats()
	},
	onShareAppMessage() {
		return {
			title: '闲置物品分类-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/classify/classify?n=n')
		}
	},
	onShareTimeline() {
		return {
			title: '闲置物品分类-' + getApp().globalData.appName
		}
	},
	methods: {
		clickCat(cat) {
			Share.openLink('/pages/idle-shop/list/list?title=分类【' + cat.name + '】&cat_id=' + cat.id)
		},
		clickGoods(goods) {
			Share.openLink('/pages/idle-shop/detail/detail?id=' + goods.id)
		},
		loadCats() {
			Rest.post(Api.URL('idle', 'cats'), {}).then(res => {
				this.cats = res.data.cats
			}, err => {
				console.log(err)
			})
		}
	}
}
</script>

<style>
.zhuige-classify {
	margin-bottom: 40rpx;
	padding: 0 20rpx;
}

.zhuige-idle-classify-header {
	align-items: center;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	padding: 20rpx 0;
}

.zhuige-idle-classify-header > view:first-child view {
	color: #333;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.zhuige-idle-classify-header > view:first-child text {
	color: #999;
	font-size: 24rpx;
}

.zhuige-idle-classify-header > view:last-child {
	background: #f5f5f5;
	border-radius: 20rpx;
	color: #666;
	font-size: 24rpx;
	padding: 12rpx 24rpx;
}

.zhuige-idle-classify-cover {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.zhuige-idle-classify-cover > view {
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	width: 48%;
}

.zhuige-idle-classify-cover > view image {
	height: 240rpx;
	width: 100%;
}
</style>
