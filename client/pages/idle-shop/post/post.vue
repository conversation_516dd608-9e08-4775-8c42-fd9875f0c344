<template>
	<view class="content">
		<!-- 商品名称 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-title-line">
					<input v-model="title" placeholder="请输入名称" type="text" />
				</view>
			</view>
		</view>
		
		<!-- 商品描述 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-input">
					<textarea v-model="content" placeholder="详细介绍有利于被推荐哦…" maxlength="140"></textarea>
				</view>
			</view>
		</view>
		
		<!-- 图片上传 -->
		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view v-if="images.length < 9" @click="clickImage">
					<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
					<view>图片</view>
				</view>
				<view class="loaded" v-for="(image, index) in images" :key="index">
					<uni-icons type="clear" size="24" color="#FD6531" @click="clickDelImage(index)"></uni-icons>
					<image mode="aspectFill" :src="image.image.url"></image>
				</view>
			</view>
		</view>
		
		<!-- 选择分类 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-check">
					<view>选择分类：</view>
					<view>
						<picker :range="cat_names" :value="cur_cat" @change="onCatChange">
							<view>
								<view class="picker">{{ cats.length > 0 && cur_cat >= 0 ? cats[cur_cat].name : '' }}</view>
								<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 销售价格 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-price">
					<view>销售价格：</view>
					<view>
						<input v-model="price" placeholder="如:9.99" type="number" />
					</view>
				</view>
			</view>
		</view>
		
		<!-- 发布协议 -->
		<view class="zhuige-creat-opt" v-if="fbxy">
			<label class="zhuige-cread-radio" @click="clickCheckFbxy">
				<radio :checked="checkFbxy" color="#111111" />
				我已阅读并同意<text @click.stop="clickFbxy">《发布协议》</text>
			</label>
		</view>
		
		<!-- 确定按钮 -->
		<view class="zhuige-base-button" @click="clickOK">
			<view>确定</view>
		</view>
	</view>
</template>

<script>
import Rest from '@/utils/Rest.js'
import Tools from '@/utils/Tools.js'
import Api from '@/utils/Api.js'
import Share from '@/utils/Share.js'

export default {
	data() {
		return {
			goods_id: 0,
			title: '',
			content: '',
			images: [],
			cats: [],
			cat_names: [],
			cur_cat: -1,
			price: '',
			fbxy: '',
			checkFbxy: false,
			user_id: 0
		}
	},
	onLoad(options) {
		this.user_id = getApp().globalData.user_id
		
		if (options.goods_id) {
			this.goods_id = parseInt(options.goods_id)
		}
		
		if (options.cat_id) {
			this.pre_cat_id = parseInt(options.cat_id)
		}
		
		this.loadCats()
	},
	onShow() {
		// 页面显示时的逻辑
	},
	onShareAppMessage() {
		return {
			title: '发布闲置-' + getApp().globalData.appName,
			path: Share.addShareSource('pages/idle-shop/post/post?n=n')
		}
	},
	onShareTimeline() {
		return {
			title: '发布闲置-' + getApp().globalData.appName
		}
	},
	methods: {
		clickImage() {
			uni.chooseImage({
				count: 9 - this.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					res.tempFilePaths.forEach(tempFilePath => {
						this.uploadImage(tempFilePath)
					})
				}
			})
		},
		clickDelImage(index) {
			this.images.splice(index, 1)
		},
		uploadImage(tempFilePath) {
			Tools.showLoading('上传中...')
			
			Rest.upload(Api.URL('upload', 'image'), tempFilePath).then(res => {
				Tools.hideLoading()
				this.images.push({
					image: res.data
				})
			}, err => {
				Tools.hideLoading()
				Tools.error(err.message)
			})
		},
		onCatChange(e) {
			this.cur_cat = parseInt(e.detail.value)
		},
		clickCheckFbxy() {
			this.checkFbxy = !this.checkFbxy
		},
		clickFbxy() {
			if (this.fbxy) {
				Share.openLink('/pages/base/webview/webview?url=' + encodeURIComponent(this.fbxy))
			}
		},
		clickOK() {
			if (!this.user_id) {
				Share.login()
				return
			}
			
			if (!this.title.trim()) {
				Tools.toast('请输入商品名称')
				return
			}
			
			if (!this.content.trim()) {
				Tools.toast('请输入商品描述')
				return
			}
			
			if (this.images.length === 0) {
				Tools.toast('请上传商品图片')
				return
			}
			
			if (this.cur_cat < 0) {
				Tools.toast('请选择商品分类')
				return
			}
			
			if (!this.price || parseFloat(this.price) <= 0) {
				Tools.toast('请输入正确的价格')
				return
			}
			
			if (this.fbxy && !this.checkFbxy) {
				Tools.toast('请先同意发布协议')
				return
			}
			
			Tools.showLoading('发布中...')
			
			const params = {
				title: this.title,
				content: this.content,
				images: JSON.stringify(this.images.map(item => item.image.url)),
				cat_id: this.cats[this.cur_cat].id,
				price: this.price
			}
			
			if (this.goods_id) {
				params.goods_id = this.goods_id
			}
			
			Rest.post(Api.URL('idle', this.goods_id ? 'edit' : 'create'), params).then(res => {
				Tools.hideLoading()
				Tools.toast(res.message)
				
				setTimeout(() => {
					if (this.goods_id) {
						Share.navigateBack()
					} else {
						Share.openLink('/pages/idle-shop/detail/detail?id=' + res.data.goods_id)
					}
				}, 1500)
			}, err => {
				Tools.hideLoading()
				Tools.error(err.message)
			})
		},
		loadCats() {
			Rest.post(Api.URL('idle', 'cats'), {}).then(res => {
				this.cats = res.data.cats || []
				this.cat_names = this.cats.map(cat => cat.name)
				
				// 设置预选分类
				if (this.pre_cat_id) {
					for (let i = 0; i < this.cats.length; i++) {
						if (this.cats[i].id === this.pre_cat_id) {
							this.cur_cat = i
							break
						}
					}
				}
				
				// 加载发布协议
				this.fbxy = res.data.fbxy || ''
				
				// 如果是编辑模式，加载商品信息
				if (this.goods_id) {
					this.loadGoodsInfo()
				}
			}, err => {
				console.log(err)
			})
		},
		loadGoodsInfo() {
			Rest.post(Api.URL('idle', 'edit_goods'), {
				goods_id: this.goods_id
			}).then(res => {
				this.title = res.data.title
				this.content = res.data.content
				this.price = res.data.price
				
				// 设置分类
				for (let i = 0; i < this.cats.length; i++) {
					if (this.cats[i].id === res.data.cat_id) {
						this.cur_cat = i
						break
					}
				}
				
				// 设置图片
				this.images = res.data.images.map(url => ({
					image: { url: url }
				}))
				
				uni.setNavigationBarTitle({
					title: '编辑商品'
				})
			}, err => {
				Tools.error(err.message)
			})
		}
	}
}
</script>

<style>
.content {
	height: 100%;
	overflow-y: scroll;
	position: fixed;
	width: 100%;
}

.zhuige-post-box {
	padding: 0 20rpx;
}

.content .zhuige-post-box:nth-last-child(2) {
	margin-bottom: 180rpx;
}

.zhuige-post-title-line {
	border-bottom: 1rpx solid #f5f5f5;
	padding: 30rpx 0;
}

.zhuige-post-title-line input {
	color: #333;
	font-size: 32rpx;
	width: 100%;
}

.zhuige-post-input {
	padding: 30rpx 0;
}

.zhuige-post-input textarea {
	color: #333;
	font-size: 28rpx;
	height: 200rpx;
	width: 100%;
}

.zhuige-upload-set {
	display: flex;
	flex-wrap: wrap;
	padding: 30rpx 0;
}

.zhuige-upload-set > view {
	align-items: center;
	background: #f8f8f8;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	height: 160rpx;
	justify-content: center;
	margin-bottom: 20rpx;
	margin-right: 20rpx;
	width: 160rpx;
}

.zhuige-upload-set > view:nth-child(3n) {
	margin-right: 0;
}

.zhuige-upload-set > view view {
	color: #777;
	font-size: 24rpx;
	margin-top: 8rpx;
}

.zhuige-upload-set .loaded {
	position: relative;
}

.zhuige-upload-set .loaded image {
	border-radius: 12rpx;
	height: 160rpx;
	width: 160rpx;
}

.zhuige-upload-set .loaded .uni-icons {
	position: absolute;
	right: -8rpx;
	top: -8rpx;
	z-index: 2;
}

.zhuige-post-line {
	align-items: center;
	border-bottom: 1rpx solid #f5f5f5;
	display: flex;
	justify-content: space-between;
	padding: 30rpx 0;
}

.zhuige-post-line > view:first-child {
	color: #333;
	font-size: 28rpx;
}

.zhuige-post-line > view:last-child {
	align-items: center;
	display: flex;
}

.line-check .picker {
	color: #666;
	font-size: 28rpx;
	margin-right: 12rpx;
}

.line-price input {
	color: #333;
	font-size: 28rpx;
	text-align: right;
	width: 200rpx;
}

.zhuige-creat-opt {
	margin: 40rpx 20rpx;
}

.zhuige-cread-radio {
	align-items: center;
	color: #666;
	display: flex;
	font-size: 26rpx;
}

.zhuige-cread-radio radio {
	margin-right: 12rpx;
}

.zhuige-cread-radio text {
	color: #007aff;
}

.zhuige-base-button {
	background: #ff6146;
	border-radius: 12rpx;
	margin: 40rpx 20rpx;
	padding: 24rpx;
	text-align: center;
}

.zhuige-base-button view {
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
}
</style>
