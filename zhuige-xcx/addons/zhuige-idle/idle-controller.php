<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Idle_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->module = 'idle';

        $this->routes = [
            // 首页配置
            'setting' => 'get_setting',
            'setting_cat' => 'get_setting_cat',

            // 列表相关
            'last' => 'get_last_posts',
            'cat' => 'get_category_posts',
            'tag' => 'get_tag_posts',
            'search' => 'get_search_posts',
            'list' => 'get_category_posts', // 兼容前端调用

            // 详情
            'detail' => 'get_detail',

            // 创建编辑
            'create_pre' => ['callback' => 'create_pre', 'auth' => 'login'],
            'create' => ['callback' => 'create', 'auth' => 'login']
        ];
    }
    
    /**
     * 获取设置信息
     */
    public function get_setting()
    {
        $settings = zhuige_idle_get_setting();
        
        // 获取导航分类
        $nav_cats = zhuige_idle_get_categories();
        $settings['nav_cats'] = $nav_cats;
        
        return $this->success($settings);
    }
    
    /**
     * 获取分类设置
     */
    public function get_setting_cat()
    {
        $cats = zhuige_idle_get_categories(true);
        
        return $this->success([
            'cats' => $cats
        ]);
    }
    
    /**
     * 获取最新商品列表
     */
    public function get_last_posts($request)
    {
        $offset = $this->param_int($request, 'offset', 0);

        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        $query = new WP_Query($args);
        $list = [];

        foreach ($query->posts as $post) {
            $list[] = zhuige_idle_shop_goods_format($post);
        }

        return $this->success([
            'list' => $list,
            'more' => (count($list) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore')
        ]);
    }
    
    /**
     * 按分类获取商品列表
     */
    public function get_category_posts($request)
    {
        $cat_id = $this->param_int($request, 'cat_id', 0);
        $offset = $this->param_int($request, 'offset', 0);

        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        // 如果指定了分类，添加分类查询
        if ($cat_id) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'zhuige_idle_goods_cat',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ]
            ];
        }

        $query = new WP_Query($args);
        $list = [];

        foreach ($query->posts as $post) {
            $list[] = zhuige_idle_shop_goods_format($post);
        }

        return $this->success([
            'list' => $list,
            'more' => (count($list) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore')
        ]);
    }
    
    /**
     * 按标签获取商品列表
     */
    public function get_tag_posts($request)
    {
        $tag_id = $this->param_int($request, 'tag_id', 0);
        $offset = $this->param_int($request, 'offset', 0);

        if (!$tag_id) {
            return $this->error('缺少参数');
        }

        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'tax_query' => [
                [
                    'taxonomy' => 'post_tag',
                    'field' => 'term_id',
                    'terms' => $tag_id,
                ]
            ]
        ];

        $query = new WP_Query($args);
        $list = [];

        foreach ($query->posts as $post) {
            $list[] = zhuige_idle_shop_goods_format($post);
        }

        return $this->success([
            'list' => $list,
            'more' => (count($list) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore')
        ]);
    }

    /**
     * 搜索商品
     */
    public function get_search_posts($request)
    {
        $search = $this->param($request, 'search', '');
        $offset = $this->param_int($request, 'offset', 0);

        if (!$search) {
            return $this->error('缺少搜索关键词');
        }

        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            's' => $search,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        $query = new WP_Query($args);
        $list = [];

        foreach ($query->posts as $post) {
            $list[] = zhuige_idle_shop_goods_format($post);
        }

        return $this->success([
            'list' => $list,
            'more' => (count($list) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore')
        ]);
    }
    
    /**
     * 获取商品详情
     */
    public function get_detail($request)
    {
        $goods_id = $this->param_int($request, 'goods_id', 0);
        $my_user_id = get_current_user_id();

        if (!$goods_id) {
            return $this->error('缺少参数');
        }

        $post = get_post($goods_id);

        if (!$post || $post->post_type !== 'zhuige_idle_goods') {
            return $this->error('商品不存在');
        }

        // 如果商品未发布，只有作者可以查看
        if ($post->post_status !== 'publish' && $post->post_author != $my_user_id) {
            return $this->error('商品不存在');
        }

        // 增加浏览量
        if ($my_user_id) {
            global $wpdb;
            $table_post_view = $wpdb->prefix . 'zhuige_xcx_post_view';
            $post_view_id = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT `id` FROM `$table_post_view` WHERE `user_id`=%d AND `post_id`=%d",
                    $my_user_id,
                    $goods_id
                )
            );
            if (!$post_view_id) {
                $wpdb->insert($table_post_view, [
                    'user_id' => $my_user_id,
                    'post_id' => $goods_id,
                    'post_status' => 'publish',
                    'time' => time()
                ]);
            }
        }

        // 文章浏览数
        $post_views = (int) get_post_meta($goods_id, 'zhuige_views', true);
        if (!update_post_meta($goods_id, 'zhuige_views', ($post_views + 1))) {
            add_post_meta($goods_id, 'zhuige_views', 1, true);
        }

        $goods = zhuige_idle_shop_goods_format($post, true);

        return $this->success($goods);
    }
    
    /**
     * 创建前预处理
     */
    public function create_pre($request)
    {
        $goods_id = $this->param_int($request, 'goods_id', 0);
        $my_user_id = get_current_user_id();

        // 黑名单检查
        if (function_exists('zhuige_auth_is_black') && zhuige_auth_is_black($my_user_id)) {
            return $this->error('操作太频繁了~');
        }

        // 检查权限
        $require_mobile = ZhuiGe_Xcx::option_value('idle_create_require_mobile', 0);
        $require_avatar = ZhuiGe_Xcx::option_value('idle_create_require_avatar', 0);
        $require_weixin = ZhuiGe_Xcx::option_value('idle_create_require_weixin', 0);
        $licence_page = ZhuiGe_Xcx::option_value('idle_create_licence', 0);

        if (!empty($require_mobile) && !zhuige_xcx_is_set_mobile($my_user_id)) {
            return $this->error('', 'require_mobile');
        }

        if (!empty($require_avatar) && !zhuige_xcx_is_set_avatar($my_user_id)) {
            return $this->error('', 'require_avatar');
        }

        if (!empty($require_weixin) && !get_user_meta($my_user_id, 'weixin', true)) {
            return $this->error('', 'require_weixin');
        }

        $data = [];

        // 分类
        $data['cats'] = zhuige_idle_get_categories();

        // 发布协议
        if ($licence_page) {
            $data['fbxy'] = '/pages/base/page/page?page_id=' . $licence_page;
        }
        
        // 如果是编辑模式，返回商品数据
        if ($goods_id) {
            $post = get_post($goods_id);
            if (!$post || $post->post_type !== 'zhuige_idle_goods') {
                return $this->error('商品不存在');
            }

            // 检查权限
            if ($post->post_author != $my_user_id && !current_user_can('edit_others_posts')) {
                return $this->error('无权限编辑此商品');
            }

            $goods = zhuige_idle_shop_goods_format($post, true);
            $data['goods'] = $goods;
        }
        
        return $this->success($data);
    }
    
    /**
     * 创建/编辑商品
     */
    public function create($request)
    {
        $goods_id = $this->param_int($request, 'goods_id', 0);
        $my_user_id = get_current_user_id();

        // 黑名单检查
        if (function_exists('zhuige_auth_is_black') && zhuige_auth_is_black($my_user_id)) {
            return $this->error('操作太频繁了~');
        }

        $title = $this->param($request, 'title', '');
        $content = $this->param($request, 'content', '');
        $images = $this->param($request, 'images', '');
        $price = floatval($this->param($request, 'price', 0));
        $cat_id = $this->param_int($request, 'cat_id', 0);

        if (empty($title)) {
            return $this->error('商品名称不能为空');
        }

        if ($price < 0.01) {
            return $this->error('价格不能小于0.01元');
        }

        if (!$cat_id) {
            return $this->error('请选择分类');
        }

        // 检查权限
        $require_mobile = ZhuiGe_Xcx::option_value('idle_create_require_mobile', 0);
        $require_avatar = ZhuiGe_Xcx::option_value('idle_create_require_avatar', 0);
        $require_weixin = ZhuiGe_Xcx::option_value('idle_create_require_weixin', 0);

        if (!empty($require_mobile) && !zhuige_xcx_is_set_mobile($my_user_id)) {
            return $this->error('', 'require_mobile');
        }

        if (!empty($require_avatar) && !zhuige_xcx_is_set_avatar($my_user_id)) {
            return $this->error('', 'require_avatar');
        }

        if (!empty($require_weixin) && !get_user_meta($my_user_id, 'weixin', true)) {
            return $this->error('', 'require_weixin');
        }
        
        if ($goods_id) {
            // 编辑模式
            $existing_post = get_post($goods_id);
            if (!$existing_post || $existing_post->post_type !== 'zhuige_idle_goods') {
                return $this->error('商品不存在');
            }

            // 检查权限
            if ($existing_post->post_author != $my_user_id && !current_user_can('edit_others_posts')) {
                return $this->error('无权限编辑此商品');
            }

            // 编辑时保持原有状态
            $status = $existing_post->post_status;

            $post_data = [
                'ID' => $goods_id,
                'post_title' => $title,
                'post_content' => $content,
                'post_excerpt' => mb_substr(strip_tags($content), 0, 100),
                'post_status' => $status,
                'post_type' => 'zhuige_idle_goods',
                'post_author' => $my_user_id
            ];

            $result = wp_update_post($post_data);
        } else {
            // 创建模式：根据设置决定是否需要审核
            $auto_approve = ZhuiGe_Xcx::option_value('idle_auto_approve', false);
            $status = $auto_approve ? 'publish' : 'pending';

            $post_data = [
                'post_title' => $title,
                'post_content' => $content,
                'post_excerpt' => mb_substr(strip_tags($content), 0, 100),
                'post_status' => $status,
                'post_type' => 'zhuige_idle_goods',
                'post_author' => $my_user_id
            ];

            $result = wp_insert_post($post_data);
            $goods_id = $result;
        }
        
        if (!$result) {
            return $this->error('系统异常');
        }

        $post_id = $goods_id;
        
        // 保存metabox数据
        $metabox_data = [
            'price' => $price,
            'images' => $images,
            'stick' => false
        ];
        update_post_meta($post_id, 'zhuige_idle_goods_options', $metabox_data);

        // 设置分类
        if ($cat_id) {
            wp_set_post_terms($post_id, [$cat_id], 'zhuige_idle_goods_cat');
        }

        // 添加积分（仅新创建时）
        if (!$goods_id && function_exists('zhuige_xcx_add_user_score_by_task')) {
            zhuige_xcx_add_user_score_by_task('idle_add', 'idle,' . $post_id);
        }

        $message = $goods_id ? '修改成功' : ($status === 'publish' ? '发布成功' : '提交成功，等待审核');

        return $this->success([
            'goods_id' => $post_id,
            'status' => $status
        ], $message);
    }
}

// 注册控制器
ZhuiGe_Xcx::$rest_controllers[] = new ZhuiGe_Xcx_Idle_Controller();
