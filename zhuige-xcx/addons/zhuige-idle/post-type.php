<?php

/**
 * 追格小程序 - 闲置物品自定义文章类型
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 注册闲置物品自定义文章类型
 */
function zhuige_idle_register_post_type()
{
    $labels = [
        'name'                  => '闲置物品',
        'singular_name'         => '闲置物品',
        'menu_name'             => '闲置物品',
        'name_admin_bar'        => '闲置物品',
        'archives'              => '闲置物品归档',
        'attributes'            => '闲置物品属性',
        'parent_item_colon'     => '父级闲置物品:',
        'all_items'             => '所有闲置物品',
        'add_new_item'          => '添加新闲置物品',
        'add_new'               => '添加新的',
        'new_item'              => '新闲置物品',
        'edit_item'             => '编辑闲置物品',
        'update_item'           => '更新闲置物品',
        'view_item'             => '查看闲置物品',
        'view_items'            => '查看闲置物品',
        'search_items'          => '搜索闲置物品',
        'not_found'             => '未找到',
        'not_found_in_trash'    => '回收站中未找到',
        'featured_image'        => '特色图片',
        'set_featured_image'    => '设置特色图片',
        'remove_featured_image' => '移除特色图片',
        'use_featured_image'    => '使用特色图片',
        'insert_into_item'      => '插入到闲置物品',
        'uploaded_to_this_item' => '上传到此闲置物品',
        'items_list'            => '闲置物品列表',
        'items_list_navigation' => '闲置物品列表导航',
        'filter_items_list'     => '过滤闲置物品列表',
    ];

    $args = [
        'label'                 => '闲置物品',
        'description'           => '闲置物品交易',
        'labels'                => $labels,
        'supports'              => ['title', 'editor', 'excerpt', 'author', 'thumbnail', 'comments', 'custom-fields'],
        'taxonomies'            => ['idle_category', 'post_tag'],
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 20,
        'menu_icon'             => 'dashicons-cart',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rest_base'             => 'idle-goods',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    ];

    register_post_type('zhuige_idle_goods', $args);
}

/**
 * 注册闲置物品分类法
 */
function zhuige_idle_register_taxonomy()
{
    $labels = [
        'name'                       => '闲置分类',
        'singular_name'              => '闲置分类',
        'menu_name'                  => '闲置分类',
        'all_items'                  => '所有分类',
        'parent_item'                => '父级分类',
        'parent_item_colon'          => '父级分类:',
        'new_item_name'              => '新分类名称',
        'add_new_item'               => '添加新分类',
        'edit_item'                  => '编辑分类',
        'update_item'                => '更新分类',
        'view_item'                  => '查看分类',
        'separate_items_with_commas' => '用逗号分隔分类',
        'add_or_remove_items'        => '添加或移除分类',
        'choose_from_most_used'      => '从最常用的分类中选择',
        'popular_items'              => '热门分类',
        'search_items'               => '搜索分类',
        'not_found'                  => '未找到',
        'no_terms'                   => '没有分类',
        'items_list'                 => '分类列表',
        'items_list_navigation'      => '分类列表导航',
    ];

    $args = [
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rest_base'                  => 'idle-categories',
        'rest_controller_class'      => 'WP_REST_Terms_Controller',
    ];

    register_taxonomy('idle_category', ['zhuige_idle_goods'], $args);
}

/**
 * 添加自定义字段元框
 */
function zhuige_idle_add_meta_boxes()
{
    add_meta_box(
        'zhuige-idle-goods-meta',
        '商品信息',
        'zhuige_idle_goods_meta_box_callback',
        'zhuige_idle_goods',
        'normal',
        'high'
    );
}

/**
 * 商品信息元框回调函数
 */
function zhuige_idle_goods_meta_box_callback($post)
{
    wp_nonce_field('zhuige_idle_goods_meta_box', 'zhuige_idle_goods_meta_box_nonce');

    $options = get_post_meta($post->ID, 'zhuige_idle_goods_options', true);
    $price = $options['price'] ?? '';
    $original_price = $options['original_price'] ?? '';
    $condition_type = $options['condition_type'] ?? 'good';
    $location = $options['location'] ?? '';
    $contact_info = $options['contact_info'] ?? '';
    $trade_type = $options['trade_type'] ?? 'both';
    $is_negotiable = $options['is_negotiable'] ?? 1;
    $stick = get_post_meta($post->ID, 'zhuige_idle_goods_stick', true);

    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="idle_price">售价</label></th>';
    echo '<td><input type="number" step="0.01" id="idle_price" name="idle_price" value="' . esc_attr($price) . '" style="width: 200px;" /> 元</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_original_price">原价</label></th>';
    echo '<td><input type="number" step="0.01" id="idle_original_price" name="idle_original_price" value="' . esc_attr($original_price) . '" style="width: 200px;" /> 元</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_condition_type">成色</label></th>';
    echo '<td>';
    echo '<select id="idle_condition_type" name="idle_condition_type">';
    echo '<option value="new"' . selected($condition_type, 'new', false) . '>全新</option>';
    echo '<option value="good"' . selected($condition_type, 'good', false) . '>九成新</option>';
    echo '<option value="fair"' . selected($condition_type, 'fair', false) . '>八成新</option>';
    echo '<option value="poor"' . selected($condition_type, 'poor', false) . '>七成新以下</option>';
    echo '</select>';
    echo '</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_location">所在地</label></th>';
    echo '<td><input type="text" id="idle_location" name="idle_location" value="' . esc_attr($location) . '" style="width: 300px;" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_contact_info">联系方式</label></th>';
    echo '<td><textarea id="idle_contact_info" name="idle_contact_info" rows="3" style="width: 300px;">' . esc_textarea($contact_info) . '</textarea></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_trade_type">交易方式</label></th>';
    echo '<td>';
    echo '<select id="idle_trade_type" name="idle_trade_type">';
    echo '<option value="both"' . selected($trade_type, 'both', false) . '>线上线下均可</option>';
    echo '<option value="online"' . selected($trade_type, 'online', false) . '>仅线上交易</option>';
    echo '<option value="offline"' . selected($trade_type, 'offline', false) . '>仅线下交易</option>';
    echo '</select>';
    echo '</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_is_negotiable">是否可议价</label></th>';
    echo '<td><input type="checkbox" id="idle_is_negotiable" name="idle_is_negotiable" value="1"' . checked($is_negotiable, 1, false) . ' /> 可议价</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="idle_stick">推广</label></th>';
    echo '<td><input type="checkbox" id="idle_stick" name="idle_stick" value="1"' . checked($stick, 1, false) . ' /> 设为推广</td>';
    echo '</tr>';
    echo '</table>';
}

/**
 * 保存自定义字段
 */
function zhuige_idle_save_meta_box_data($post_id)
{
    if (!isset($_POST['zhuige_idle_goods_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['zhuige_idle_goods_meta_box_nonce'], 'zhuige_idle_goods_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && 'zhuige_idle_goods' == $_POST['post_type']) {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    $options = [];
    $options['price'] = isset($_POST['idle_price']) ? sanitize_text_field($_POST['idle_price']) : '';
    $options['original_price'] = isset($_POST['idle_original_price']) ? sanitize_text_field($_POST['idle_original_price']) : '';
    $options['condition_type'] = isset($_POST['idle_condition_type']) ? sanitize_text_field($_POST['idle_condition_type']) : 'good';
    $options['location'] = isset($_POST['idle_location']) ? sanitize_text_field($_POST['idle_location']) : '';
    $options['contact_info'] = isset($_POST['idle_contact_info']) ? sanitize_textarea_field($_POST['idle_contact_info']) : '';
    $options['trade_type'] = isset($_POST['idle_trade_type']) ? sanitize_text_field($_POST['idle_trade_type']) : 'both';
    $options['is_negotiable'] = isset($_POST['idle_is_negotiable']) ? 1 : 0;

    update_post_meta($post_id, 'zhuige_idle_goods_options', $options);
    
    $stick = isset($_POST['idle_stick']) ? 1 : 0;
    update_post_meta($post_id, 'zhuige_idle_goods_stick', $stick);
}

// 注册钩子
add_action('init', 'zhuige_idle_register_post_type', 0);
add_action('init', 'zhuige_idle_register_taxonomy', 0);
add_action('add_meta_boxes', 'zhuige_idle_add_meta_boxes');
add_action('save_post', 'zhuige_idle_save_meta_box_data');
