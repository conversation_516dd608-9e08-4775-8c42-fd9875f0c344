<?php

/**
 * 追格小程序 - 闲置物品后台管理
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 闲置物品
//
CSF::createSection($prefix, array(
    'id'    => 'idle',
    'title' => '闲置物品',
    'icon'  => 'fas fa-shopping-cart',
));

//
// 首页设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '首页设置',
    'icon'  => 'fas fa-home',
    'fields' => array(

        array(
            'id'     => 'idle_slides',
            'type'   => 'group',
            'title'  => '幻灯片',
            'subtitle' => '在闲置物品首页顶部显示的轮播图片',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/216.html" target="_blank">图片规格建议</a>',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                    'default' => 'https://www.zhuige.com',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/215.html" target="_blank">链接格式说明</a>',
                ),
            ),
        ),

        array(
            'id'     => 'idle_icons',
            'type'   => 'group',
            'title'  => '导航图标',
            'subtitle' => '在闲置物品首页显示的导航图标',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'      => 'idle_background',
            'type'    => 'media',
            'title'   => '背景图片',
            'subtitle' => '闲置物品首页背景图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'idle_bottom_menu',
            'type'   => 'group',
            'title'  => '底部菜单',
            'subtitle' => '在闲置物品首页底部显示的菜单',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

    ),
));

//
// 基本设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '基本设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'      => 'idle_enable',
            'type'    => 'switcher',
            'title'   => '启用闲置物品模块',
            'default' => true,
        ),

        array(
            'id'      => 'idle_page_title',
            'type'    => 'text',
            'title'   => '页面标题',
            'default' => '闲置物品',
        ),

        array(
            'id'      => 'idle_page_desc',
            'type'    => 'textarea',
            'title'   => '页面描述',
            'default' => '发现身边的闲置好物',
        ),

        array(
            'id'      => 'idle_allow_guest_view',
            'type'    => 'switcher',
            'title'   => '允许游客浏览',
            'default' => true,
        ),

        array(
            'id'      => 'idle_allow_guest_post',
            'type'    => 'switcher',
            'title'   => '允许游客发布',
            'default' => false,
        ),

        array(
            'id'      => 'idle_auto_approve',
            'type'    => 'switcher',
            'title'   => '自动审核通过',
            'default' => true,
        ),

        array(
            'id'      => 'idle_max_images',
            'type'    => 'number',
            'title'   => '最大图片数量',
            'default' => 9,
            'min'     => 1,
            'max'     => 20,
        ),

        array(
            'id'    => 'idle_post_agreement',
            'type'  => 'text',
            'title' => '发布协议链接',
            'subtitle' => '留空则不显示协议',
        ),

        array(
            'id'      => 'idle_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '分享时显示的图片',
            'library' => 'image',
        ),

    ),
));

//
// 列表设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '列表设置',
    'icon'  => 'fas fa-list',
    'fields' => array(

        array(
            'id'      => 'idle_list_style',
            'type'    => 'radio',
            'title'   => '列表样式',
            'options' => array(
                'waterfall' => '瀑布流',
                'list'      => '列表',
            ),
            'default' => 'waterfall',
        ),

        array(
            'id'      => 'idle_posts_per_page',
            'type'    => 'number',
            'title'   => '每页显示数量',
            'default' => 20,
            'min'     => 1,
            'max'     => 100,
        ),

        array(
            'id'      => 'idle_show_author',
            'type'    => 'switcher',
            'title'   => '显示发布者信息',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_price',
            'type'    => 'switcher',
            'title'   => '显示价格',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_tags',
            'type'    => 'switcher',
            'title'   => '显示标签',
            'default' => true,
        ),

    ),
));

//
// 详情设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '详情设置',
    'icon'  => 'fas fa-eye',
    'fields' => array(

        array(
            'id'      => 'idle_show_views',
            'type'    => 'switcher',
            'title'   => '显示浏览量',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_like',
            'type'    => 'switcher',
            'title'   => '显示点赞功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_favorite',
            'type'    => 'switcher',
            'title'   => '显示收藏功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_comment',
            'type'    => 'switcher',
            'title'   => '显示评论功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_share',
            'type'    => 'switcher',
            'title'   => '显示分享功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_recommend',
            'type'    => 'switcher',
            'title'   => '显示推荐商品',
            'default' => true,
        ),

        array(
            'id'      => 'idle_recommend_count',
            'type'    => 'number',
            'title'   => '推荐商品数量',
            'default' => 4,
            'min'     => 1,
            'max'     => 10,
            'dependency' => array('idle_show_recommend', '==', true),
        ),

    ),
));
