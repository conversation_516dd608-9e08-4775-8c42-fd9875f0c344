<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 闲置物品
//
CSF::createSection($prefix, array(
    'id'    => 'idle',
    'title' => '闲置物品',
    'icon'  => 'fas fa-shopping-bag',
));

//
// 闲置物品设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '基本设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'      => 'idle_enable',
            'type'    => 'switcher',
            'title'   => '启用闲置物品模块',
            'subtitle' => '是否启用闲置物品功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_page_title',
            'type'    => 'text',
            'title'   => '页面标题',
            'subtitle' => '闲置物品页面的标题',
            'default' => '闲置物品',
        ),

        array(
            'id'      => 'idle_page_desc',
            'type'    => 'textarea',
            'title'   => '页面描述',
            'subtitle' => '闲置物品页面的描述',
            'default' => '发现身边的闲置好物',
        ),

        array(
            'id'      => 'idle_allow_guest_view',
            'type'    => 'switcher',
            'title'   => '允许游客浏览',
            'subtitle' => '是否允许未登录用户浏览商品',
            'default' => true,
        ),

        array(
            'id'      => 'idle_allow_guest_post',
            'type'    => 'switcher',
            'title'   => '允许游客发布',
            'subtitle' => '是否允许未登录用户发布商品',
            'default' => false,
        ),

        array(
            'id'      => 'idle_auto_approve',
            'type'    => 'switcher',
            'title'   => '自动审核通过',
            'subtitle' => '新发布的商品是否自动审核通过',
            'default' => false,
        ),

        array(
            'id'      => 'idle_max_images',
            'type'    => 'number',
            'title'   => '最大图片数量',
            'subtitle' => '每个商品最多可上传的图片数量',
            'default' => 9,
            'min'     => 1,
            'max'     => 20,
        ),

    )
));

//
// 首页设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '首页设置',
    'icon'  => 'fas fa-home',
    'fields' => array(

        array(
            'id'      => 'idle_background',
            'type'    => 'media',
            'title'   => '背景图片',
            'subtitle' => '首页背景图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'idle_slides',
            'type'   => 'group',
            'title'  => '轮播图',
            'subtitle' => '首页轮播图配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'     => 'idle_icons',
            'type'   => 'group',
            'title'  => '图标导航',
            'subtitle' => '首页图标导航配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'     => 'idle_list_ad',
            'type'   => 'group',
            'title'  => '列表广告',
            'subtitle' => '商品列表中的广告配置',
            'fields' => array(
                array(
                    'id'    => 'first',
                    'type'  => 'number',
                    'title' => '首次出现位置',
                    'default' => 3
                ),

                array(
                    'id'    => 'frequency',
                    'type'  => 'number',
                    'title' => '出现频率',
                    'default' => 10
                ),

                array(
                    'id'     => 'items',
                    'type'   => 'group',
                    'title'  => '广告项目',
                    'fields' => array(
                        array(
                            'id'    => 'title',
                            'type'  => 'text',
                            'title' => '标题',
                        ),

                        array(
                            'id'      => 'image',
                            'type'    => 'media',
                            'title'   => '图片',
                            'library' => 'image',
                        ),

                        array(
                            'id'    => 'link',
                            'type'  => 'text',
                            'title' => '链接',
                        ),
                    ),
                ),
            ),
        ),

        array(
            'id'     => 'idle_bottom_menu',
            'type'   => 'group',
            'title'  => '底部菜单',
            'subtitle' => '首页底部菜单配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'      => 'idle_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '分享时使用的默认图片',
            'library' => 'image',
        ),

        array(
            'id'    => 'idle_create_require_mobile',
            'type'  => 'switcher',
            'title' => '发布要求手机号',
            'subtitle' => '发布商品是否要求绑定手机号',
            'default' => '0'
        ),

        array(
            'id'    => 'idle_create_require_avatar',
            'type'  => 'switcher',
            'title' => '发布要求头像昵称',
            'subtitle' => '发布商品是否要求设置头像昵称',
            'default' => '0'
        ),

        array(
            'id'    => 'idle_create_require_weixin',
            'type'  => 'switcher',
            'title' => '发布要求微信二维码',
            'subtitle' => '发布商品是否要求设置微信二维码',
            'default' => '0'
        ),

        array(
            'id'          => 'idle_create_licence',
            'type'        => 'select',
            'title'       => '发布协议',
            'subtitle'    => '用户发布商品时需要同意的协议页面',
            'chosen'      => true,
            'ajax'        => true,
            'options'     => 'pages',
            'placeholder' => '选择一个页面',
        ),
    )
));

//
// 列表设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '列表设置',
    'icon'  => 'fas fa-list',
    'fields' => array(

        array(
            'id'      => 'idle_list_style',
            'type'    => 'radio',
            'title'   => '列表样式',
            'subtitle' => '商品列表的显示样式',
            'options' => array(
                'waterfall' => '瀑布流',
                'list'      => '列表',
            ),
            'default' => 'waterfall',
        ),

        array(
            'id'      => 'idle_posts_per_page',
            'type'    => 'number',
            'title'   => '每页显示数量',
            'subtitle' => '每页显示的商品数量',
            'default' => 20,
            'min'     => 1,
            'max'     => 100,
        ),

        array(
            'id'      => 'idle_show_author',
            'type'    => 'switcher',
            'title'   => '显示发布者信息',
            'subtitle' => '是否在列表中显示发布者信息',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_price',
            'type'    => 'switcher',
            'title'   => '显示价格',
            'subtitle' => '是否在列表中显示商品价格',
            'default' => true,
        ),

    )
));

//
// 详情设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '详情设置',
    'icon'  => 'fas fa-eye',
    'fields' => array(

        array(
            'id'      => 'idle_show_views',
            'type'    => 'switcher',
            'title'   => '显示浏览量',
            'subtitle' => '是否在详情页显示浏览量',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_like',
            'type'    => 'switcher',
            'title'   => '显示点赞功能',
            'subtitle' => '是否在详情页显示点赞功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_favorite',
            'type'    => 'switcher',
            'title'   => '显示收藏功能',
            'subtitle' => '是否在详情页显示收藏功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_comment',
            'type'    => 'switcher',
            'title'   => '显示评论功能',
            'subtitle' => '是否在详情页显示评论功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_show_share',
            'type'    => 'switcher',
            'title'   => '显示分享功能',
            'subtitle' => '是否在详情页显示分享功能',
            'default' => true,
        ),

        array(
            'id'      => 'idle_comment_require_mobile',
            'type'    => 'switcher',
            'title'   => '评论要求手机号',
            'subtitle' => '评论是否要求绑定手机号',
            'default' => false,
        ),

        array(
            'id'      => 'idle_comment_require_avatar',
            'type'    => 'switcher',
            'title'   => '评论要求头像昵称',
            'subtitle' => '评论是否要求设置头像昵称',
            'default' => false,
        ),

    )
));








